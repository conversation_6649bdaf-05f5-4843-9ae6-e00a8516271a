import React, { forwardRef } from 'react';
import clsx from 'clsx';

const BubbleTextareaWithIcon = forwardRef(({
  icon: IconComponent,
  onIconClick,
  iconClassName = '',
  iconButtonClassName = '',
  className = '',
  containerClassName = '',
  placeholder = '',
  rows = 1,
  disabled = false,
  value,
  onChange,
  onKeyDown,
  iconAriaLabel = 'Icon button',
  iconTabIndex = 0,
  showIcon = true,
  ...props
}, ref) => {
  return (
    <div className={clsx(
      'bubble-input-container relative rounded-size0 w-full flex flex-col gap-size1 bg-[--dt-color-surface-100] animate-fadeInUpDelayed3 h-[42px] w-[calc(100%-8px)] mx-auto my-[4px] px-[2px]',
      containerClassName
    )}>
      <textarea
        ref={ref}
        className={clsx(
          'rounded-full px-3 py-2 transition-colors duration-200 text-[#000] no-scrollbar',
          disabled 
            ? '!bg-border-[rgba(9, 8, 31,0.05)] border-[rgba(9, 8, 31,0.5)] border-0 cursor-not-allowed' 
            : 'bg-trasnparent text-grey-75 border border-[rgba(9, 8, 31,0.1)] focus:border-purple-300 focus:ring-[0.5px] focus:ring-purple-300 focus:outline-none',
          showIcon && IconComponent && 'pr-10',
          className
        )}
        rows={rows}
        placeholder={placeholder}
        style={{
          resize: 'none',
          overflowY: 'auto',
          scrollbarWidth: 'none', /* Firefox */
          msOverflowStyle: 'none', /* IE and Edge */
          '&::-webkit-scrollbar': {
            display: 'none' /* Chrome, Safari and Opera */
          }
        }}
        value={value}
        disabled={disabled}
        onChange={onChange}
        onKeyDown={onKeyDown}
        {...props}
      />
      
      {showIcon && IconComponent && (
        <div className="absolute bottom-0 top-0 my-auto right-[6px] flex items-center">
          <button
            type="button"
            onClick={onIconClick}
            className={clsx(
              'p-size1 size-8 rounded-size0 flex items-center justify-center text-gray-500 hover:text-gray-700 transition-colors focus:outline-none',
              disabled && 'cursor-not-allowed opacity-50',
              iconButtonClassName
            )}
            aria-label={iconAriaLabel}
            tabIndex={iconTabIndex}
            disabled={disabled}
          >
            <IconComponent className={clsx('w-4 h-4', iconClassName)} />
          </button>
        </div>
      )}
    </div>
  );
});

BubbleTextareaWithIcon.displayName = 'BubbleTextareaWithIcon';

export default BubbleTextareaWithIcon;
